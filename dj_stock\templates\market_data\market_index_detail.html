{% extends "base.html" %}
{% block title %}{{ index_name }} | 股票数据分析系统{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <div>
      <h2>{{ index_name }} <small class="text-muted">({{ index_code }})</small></h2>
      <p class="text-muted">股票市场指数历史数据</p>
    </div>
    <a href="{% url 'market_data:market_index_list' %}" class="btn btn-outline-primary">
      <i class="bi bi-arrow-left"></i> 返回指数列表
    </a>
  </div>

  <!-- 日期范围筛选 -->
  <div class="card shadow-sm mb-4">
    <div class="card-body">
      <form method="get" class="row g-3 align-items-end">
        <div class="col-md-4">
          <label for="start_date" class="form-label">开始日期</label>
          <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date }}">
        </div>
        <div class="col-md-4">
          <label for="end_date" class="form-label">结束日期</label>
          <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date }}">
        </div>
        <div class="col-md-2">
          <label for="sort" class="form-label">排序方式</label>
          <select class="form-select" id="sort" name="sort">
            <option value="-trade_date" {% if sort_by == '-trade_date' %}selected{% endif %}>日期 (最新优先)</option>
            <option value="trade_date" {% if sort_by == 'trade_date' %}selected{% endif %}>日期 (最旧优先)</option>
            <option value="-close_price" {% if sort_by == '-close_price' %}selected{% endif %}>收盘价 (高到低)</option>
            <option value="close_price" {% if sort_by == 'close_price' %}selected{% endif %}>收盘价 (低到高)</option>
            <option value="-change_percent" {% if sort_by == '-change_percent' %}selected{% endif %}>涨跌幅 (高到低)</option>
            <option value="change_percent" {% if sort_by == 'change_percent' %}selected{% endif %}>涨跌幅 (低到高)</option>
          </select>
        </div>
        <div class="col-md-2">
          <button type="submit" class="btn btn-primary w-100">筛选</button>
        </div>
      </form>
    </div>
  </div>

  <!-- 数据表格 -->
  <div class="card shadow-sm mb-4">
    <div class="card-header bg-light">
      <div class="d-flex justify-content-between align-items-center">
        <span>共找到 {{ total_items }} 条记录</span>
      </div>
    </div>
    <div class="card-body">
      <div class="table-responsive">
        <table class="table table-hover align-middle">
          <thead class="table-light">
            <tr>
              <th>交易日期</th>
              <th>开盘价</th>
              <th>收盘价</th>
              <th>最高价</th>
              <th>最低价</th>
              <th>前收价</th>
              <th>涨跌幅</th>
              <th>成交量(亿)</th>
              <th>成交额(亿)</th>
            </tr>
          </thead>
          <tbody>
            {% for index in page_obj %}
            <tr>
              <td>{{ index.trade_date|date:"Y-m-d" }}</td>
              <td>{{ index.open_price|floatformat:2 }}</td>
              <td>{{ index.close_price|floatformat:2 }}</td>
              <td>{{ index.high_price|floatformat:2 }}</td>
              <td>{{ index.low_price|floatformat:2 }}</td>
              <td>{{ index.pre_close|floatformat:2 }}</td>
              <td class="{% if index.change_percent > 0 %}text-danger{% elif index.change_percent < 0 %}text-success{% endif %}">
                {{ index.change_percent|floatformat:2 }}%
              </td>
              <td>{{ index.volume|floatformat:2 }}</td>
              <td>{{ index.amount|floatformat:2 }}</td>
            </tr>
            {% empty %}
            <tr>
              <td colspan="9" class="text-center py-3">没有找到历史数据</td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
    {% include 'includes/pagination.html' with page_obj=page_obj rows_per_page=rows_per_page %}
  </div>
</div>
{% endblock %}
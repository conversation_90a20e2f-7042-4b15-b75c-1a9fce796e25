/*
 * 自定义样式表 - 优化所有页面显示效果
 * 作者: Augment Agent
 * 版本: 1.0.0
 */

/* ========== 1. 全局样式优化 ========== */

:root {
  /* 主色调 */
  --tblr-primary: #1a73e8;
  --tblr-primary-rgb: 26, 115, 232;
  --tblr-primary-fg: #fff;

  /* 涨跌颜色 - 符合中国市场习惯 */
  --color-up: #f44336;
  --color-down: #4caf50;

  /* 图表颜色 */
  --chart-color-1: #1a73e8;
  --chart-color-2: #34a853;
  --chart-color-3: #fbbc05;
  --chart-color-4: #ea4335;
  --chart-color-5: #9c27b0;

  /* 阴影效果 */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  --shadow-md: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
  --shadow-lg: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);

  /* 动画时间 */
  --transition-fast: 0.15s;
  --transition-normal: 0.3s;
  --transition-slow: 0.5s;
}

/* 页面背景色 */
body {
  background-color: #f5f7fb;
}

/* 涨跌颜色样式 */
.text-up {
  color: var(--color-up) !important;
  font-weight: 500;
}

.text-down {
  color: var(--color-down) !important;
  font-weight: 500;
}

/* 背景色涨跌 */
.bg-up {
  background-color: rgba(244, 67, 54, 0.1);
}

.bg-down {
  background-color: rgba(76, 175, 80, 0.1);
}

/* 表格行涨跌色 */
tr.row-up {
  background-color: rgba(244, 67, 54, 0.05);
}

tr.row-down {
  background-color: rgba(76, 175, 80, 0.05);
}

/* 按钮样式增强 */
.btn {
  font-weight: 500;
  letter-spacing: 0.3px;
  transition: all var(--transition-normal);
}

.btn-primary {
  background-color: var(--tblr-primary);
  border-color: var(--tblr-primary);
}

.btn-primary:hover {
  background-color: #1967d2;
  border-color: #1967d2;
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.btn-outline-primary {
  color: var(--tblr-primary);
  border-color: var(--tblr-primary);
}

.btn-outline-primary:hover {
  background-color: var(--tblr-primary);
  border-color: var(--tblr-primary);
  color: white;
}

/* 表单控件美化 */
.form-control,
.form-select {
  border-radius: 4px;
  border-color: #dce0e6;
  transition: all var(--transition-fast);
}

.form-control:focus,
.form-select:focus {
  border-color: var(--tblr-primary);
  box-shadow: 0 0 0 0.25rem rgba(var(--tblr-primary-rgb), 0.15);
}

/* ========== 2. 卡片样式优化 ========== */

/* 卡片基础样式 */
.card {
  border: none;
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
  overflow: hidden;
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

/* 卡片标题 */
.card-title {
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1rem;
}

/* 卡片头部 */
.card-header {
  background-color: transparent;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 1.25rem 1.5rem;
}

/* 卡片底部 */
.card-footer {
  background-color: transparent;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding: 1rem 1.5rem;
}

/* 数据卡片 */
.card-sm {
  border-radius: 6px;
}

/* 统计卡片 */
.stat-card .card-body {
  padding: 1.5rem;
}

.stat-card .subheader {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.stat-card .h1 {
  font-size: 1.75rem;
  font-weight: 600;
  margin: 0.75rem 0;
}

/* ========== 3. 表格样式优化 ========== */

/* 表格基础样式 */
.table {
  margin-bottom: 0;
  font-size: 0.875rem;
}

/* 表头样式 */
.table thead th {
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.5px;
  padding: 0.75rem 1rem;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

/* 表格行样式 */
.table tbody tr {
  transition: background-color var(--transition-fast);
}

.table tbody tr:hover {
  background-color: rgba(var(--tblr-primary-rgb), 0.03);
}

/* 表格单元格样式 */
.table td {
  padding: 0.75rem 1rem;
  vertical-align: middle;
  border-bottom: 1px solid #e2e8f0;
}

/* 紧凑型表格 */
.table-sm td,
.table-sm th {
  padding: 0.5rem 0.75rem;
}

/* 响应式表格 */
.table-responsive {
  border-radius: 8px;
  overflow: hidden;
}

/* 分页样式 */
.pagination {
  margin-bottom: 0;
}

.page-link {
  color: var(--tblr-primary);
  border-color: #e2e8f0;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

.page-item.active .page-link {
  background-color: var(--tblr-primary);
  border-color: var(--tblr-primary);
}

/* ========== 4. 导航和页面结构 ========== */

/* 页面标题 */
.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.25rem;
}

/* 页面副标题 */
.page-subtitle {
  color: #64748b;
  font-size: 0.875rem;
}

/* 页面头部 */
.page-header {
  padding: 1.5rem 0;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

/* 页面主体 */
.page-body {
  padding-bottom: 2rem;
}

/* 导航菜单容器 */
.navbar-vertical {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border-right: 1px solid rgba(0, 0, 0, 0.05);
}

/* 导航菜单项 */
.navbar-vertical .nav-link {
  font-weight: 500;
  padding: 0.625rem 1rem;
  border-radius: 6px;
  transition: all var(--transition-fast);
  position: relative;
  margin: 0.125rem 0;
}

/* 一级菜单组标题 */
.navbar-vertical .navbar-nav > .nav-item {
  margin-bottom: 0.25rem;
}

.navbar-vertical .nav-link:hover {
  background-color: rgba(var(--tblr-primary-rgb), 0.05);
}

.navbar-vertical .nav-link.active {
  background-color: rgba(var(--tblr-primary-rgb), 0.1);
  color: var(--tblr-primary);
  font-weight: 600;
  box-shadow: 0 0.125rem 0.25rem rgba(var(--tblr-primary-rgb), 0.1);
}

/* 当前页面菜单项的左侧标记 */
.navbar-vertical .nav-link.active:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.375rem;
  bottom: 0.375rem;
  width: 3px;
  background-color: var(--tblr-primary);
  border-radius: 0 3px 3px 0;
}

/* 二级菜单缩进和样式 */
.navbar-vertical .collapse .nav-sm {
  padding-left: 1.5rem;
  position: relative;
}

/* 二级菜单的左侧线条 */
.navbar-vertical .collapse .nav-sm:before {
  content: '';
  position: absolute;
  left: 0.75rem;
  top: 0.5rem;
  bottom: 0.5rem;
  width: 1px;
  background-color: rgba(var(--tblr-primary-rgb), 0.15);
  border-radius: 0;
}

/* 二级菜单项样式 */
.navbar-vertical .nav-sm .nav-link {
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  border-radius: 4px;
}

/* 二级菜单项图标样式 */
.navbar-vertical .nav-sm .nav-link i {
  font-size: 0.875rem;
  opacity: 0.75;
  position: relative;
}

/* 二级菜单项前的小圆点 */
.navbar-vertical .nav-sm .nav-item:before {
  content: '';
  display: inline-block;
  width: 4px;
  height: 4px;
  background-color: rgba(var(--tblr-primary-rgb), 0.5);
  border-radius: 50%;
  position: absolute;
  left: 0.5rem;
  top: 1rem;
  opacity: 0.5;
}

/* 子菜单项当前页面样式 */
.navbar-vertical .nav-sm .nav-link.active {
  background-color: rgba(var(--tblr-primary-rgb), 0.08);
  font-weight: 600;
  color: var(--tblr-primary);
}

/* 子菜单项当前页面的左侧标记 */
.navbar-vertical .nav-sm .nav-link.active:before {
  width: 2px;
  left: -0.75rem;
  top: 0.25rem;
  bottom: 0.25rem;
}

/* 当子菜单项激活时，父菜单项也需要激活 */
.navbar-vertical .nav-item.has-active-child > .nav-link {
  color: var(--tblr-primary);
  font-weight: 600;
  background-color: rgba(var(--tblr-primary-rgb), 0.05);
}

/* 菜单折叠/展开图标样式 */
.navbar-vertical .nav-link-toggle {
  margin-left: auto;
  padding-left: 0.5rem;
  transition: transform var(--transition-fast);
}

/* 展开状态的图标旋转 */
.navbar-vertical .nav-link[aria-expanded='true'] .nav-link-toggle {
  transform: rotate(90deg);
}

/* 折叠菜单的过渡效果 */
.navbar-vertical .collapse {
  transition: all 0.2s ease-in-out;
}

/* 菜单切换点击效果 */
.menu-toggle-clicked {
  animation: pulse-light 0.3s ease-in-out;
}

@keyframes pulse-light {
  0% {
    background-color: transparent;
  }
  50% {
    background-color: rgba(var(--tblr-primary-rgb), 0.1);
  }
  100% {
    background-color: transparent;
  }
}

/* 菜单项高亮效果 */
.menu-item-highlight {
  animation: highlight-item 0.5s ease-in-out;
}

@keyframes highlight-item {
  0% {
    background-color: transparent;
  }
  30% {
    background-color: rgba(var(--tblr-primary-rgb), 0.08);
  }
  100% {
    background-color: transparent;
  }
}

/* ========== 5. 数据可视化增强 ========== */

/* 徽章样式 */
.badge {
  font-weight: 500;
  padding: 0.35em 0.65em;
  border-radius: 4px;
}

/* 状态指示器 */
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.status-indicator.positive {
  background-color: var(--color-up);
}

.status-indicator.negative {
  background-color: var(--color-down);
}

.status-indicator.neutral {
  background-color: #9e9e9e;
}

/* 进度条样式 */
.progress {
  height: 0.5rem;
  border-radius: 1rem;
  background-color: #e2e8f0;
}

.progress-bar {
  background-color: var(--tblr-primary);
}

/* 空状态美化 */
.empty {
  padding: 2rem;
  text-align: center;
}

.empty-img {
  margin-bottom: 1.5rem;
  opacity: 0.5;
}

.empty-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #1e293b;
}

.empty-subtitle {
  color: #64748b;
  max-width: 20rem;
  margin: 0 auto;
}

/* ========== 6. 响应式优化 ========== */

/* 小屏幕优化 */
@media (max-width: 767.98px) {
  .card {
    margin-bottom: 1rem;
  }

  .page-title {
    font-size: 1.25rem;
  }

  .h1 {
    font-size: 1.5rem !important;
  }

  .table-responsive {
    margin-bottom: 1rem;
  }

  .page-header {
    padding: 1rem 0;
  }
}

/* 打印样式优化 */
@media print {
  .d-print-none {
    display: none !important;
  }

  .card {
    box-shadow: none !important;
    border: 1px solid #ddd;
  }

  .table thead th {
    background-color: #f1f1f1 !important;
    color: #000 !important;
  }
}

/* ========== 7. 动画效果 ========== */

/* 淡入效果 */
.fade-in {
  animation: fadeIn var(--transition-normal);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 滑入效果 */
.slide-in {
  animation: slideIn var(--transition-normal);
}

@keyframes slideIn {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 脉冲效果 */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* ========== 8. 工具类 ========== */

/* 文本截断 */
.text-truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 鼠标悬停效果 */
.hover-lift {
  transition: transform var(--transition-normal);
}

.hover-lift:hover {
  transform: translateY(-3px);
}

/* 阴影效果 */
.shadow-hover {
  transition: box-shadow var(--transition-normal);
}

.shadow-hover:hover {
  box-shadow: var(--shadow-md);
}

/* 强调文本 */
.text-emphasis {
  font-weight: 600;
  color: #1e293b;
}

/* 次要文本 */
.text-secondary {
  color: #64748b !important;
}

/* 数值格式化 */
.number {
  font-variant-numeric: tabular-nums;
  font-feature-settings: 'tnum';
}

/* 百分比变化指示器 */
.percent-change {
  display: inline-flex;
  align-items: center;
  font-weight: 500;
}

.percent-change.positive {
  color: var(--color-up);
}

.percent-change.positive:before {
  content: '▲';
  font-size: 0.75em;
  margin-right: 0.25rem;
}

.percent-change.negative {
  color: var(--color-down);
}

.percent-change.negative:before {
  content: '▼';
  font-size: 0.75em;
  margin-right: 0.25rem;
}



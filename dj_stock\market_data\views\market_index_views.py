# -*- coding: utf-8 -*-
from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.db.models import Q
from django.core.paginator import Paginator
from datetime import datetime

from .base_views import StockMarketIndex


def market_index_list(request):
    """市场指数列表视图"""
    # 获取查询参数
    search_query = request.GET.get("search", "")
    date_str = request.GET.get("date", "")
    sort_by = request.GET.get("sort", "-trade_date")

    # 获取可用日期列表
    available_dates = (
        StockMarketIndex.objects.values_list("trade_date", flat=True)
        .distinct()
        .order_by("-trade_date")
    )

    # 构建查询
    indices = StockMarketIndex.objects.all()

    if search_query:
        indices = indices.filter(
            Q(index_code__icontains=search_query)
            | Q(index_name__icontains=search_query)
        )

    selected_date = None
    if date_str:
        try:
            selected_date = datetime.strptime(date_str, "%Y-%m-%d").date()
            indices = indices.filter(trade_date=selected_date)
        except ValueError:
            pass
    else:
        # 如果没有指定日期，使用最新日期
        if available_dates.exists():
            selected_date = available_dates.first()
            indices = indices.filter(trade_date=selected_date)

    # 应用排序
    if sort_by.startswith("-"):
        sort_field = sort_by[1:]
        indices = indices.order_by(f"-{sort_field}")
    else:
        indices = indices.order_by(sort_by)

    # 分页
    rows_per_page = int(request.GET.get("rows_per_page", 10))
    paginator = Paginator(indices, rows_per_page)
    page_number = request.GET.get("page", 1)
    page_obj = paginator.get_page(page_number)

    # 处理数据单位
    for index in page_obj:
        # 计算涨跌幅 - 已经是百分比，不需要再乘以100
        if (
            index.close_price
            and index.pre_close
            and index.pre_close > 0
            and not hasattr(index, "change_percent")
        ):
            index.change_percent = (
                (float(index.close_price) - float(index.pre_close))
                / float(index.pre_close)
                * 100
            )

        # 成交量转为亿手
        if index.volume:
            index.volume = float(index.volume) / 100000000
        # 成交额转为亿元
        if index.amount:
            index.amount = float(index.amount) / 100000000

    context = {
        "page_obj": page_obj,
        "total_items": indices.count(),
        "search_query": search_query,
        "available_dates": available_dates[:30],
        "selected_date": selected_date,
        "sort_by": sort_by,
        "rows_per_page": rows_per_page,
    }
    return render(request, "market_data/market_index_list.html", context)


def market_index_detail(request, code):
    """市场指数详情视图"""
    # 获取查询参数
    start_date_str = request.GET.get("start_date", "")
    end_date_str = request.GET.get("end_date", "")
    sort_by = request.GET.get("sort", "-trade_date")

    # 获取指数基本信息
    indices = StockMarketIndex.objects.filter(index_code=code)
    if not indices.exists():
        return render(request, "market_data/error.html", {"message": "指数不存在"})

    index_info = indices.first()
    index_name = index_info.index_name

    # 构建日期范围查询
    if start_date_str:
        try:
            start_date = datetime.strptime(start_date_str, "%Y-%m-%d").date()
            indices = indices.filter(trade_date__gte=start_date)
        except ValueError:
            pass

    if end_date_str:
        try:
            end_date = datetime.strptime(end_date_str, "%Y-%m-%d").date()
            indices = indices.filter(trade_date__lte=end_date)
        except ValueError:
            pass

    # 应用排序
    if sort_by.startswith("-"):
        sort_field = sort_by[1:]
        indices = indices.order_by(f"-{sort_field}")
    else:
        indices = indices.order_by(sort_by)

    # 分页
    rows_per_page = int(request.GET.get("rows_per_page", 20))
    paginator = Paginator(indices, rows_per_page)
    page_number = request.GET.get("page", 1)
    page_obj = paginator.get_page(page_number)

    # 处理数据单位
    for index in page_obj:
        # 计算涨跌幅 - 已经是百分比，不需要再乘以100
        if (
            index.close_price
            and index.pre_close
            and index.pre_close > 0
            and not hasattr(index, "change_percent")
        ):
            index.change_percent = (
                (float(index.close_price) - float(index.pre_close))
                / float(index.pre_close)
                * 100
            )

        # 成交量转为亿手
        if index.volume:
            index.volume = float(index.volume) / 100000000
        # 成交额转为亿元
        if index.amount:
            index.amount = float(index.amount) / 100000000

    context = {
        "index_code": code,
        "index_name": index_name,
        "page_obj": page_obj,
        "total_items": indices.count(),
        "start_date": start_date_str,
        "end_date": end_date_str,
        "sort_by": sort_by,
        "rows_per_page": rows_per_page,
    }
    return render(request, "market_data/market_index_detail.html", context)

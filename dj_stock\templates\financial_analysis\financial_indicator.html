{% extends "base.html" %} {% block title %}{{ stock_name }}({{ stock_code }}) - 财务指标 - 股票数据分析系统{% endblock %} {% block content %}
<div class="container-xl">
  <div class="page-header d-print-none">
    <div class="row align-items-center">
      <div class="col">
        <h2 class="page-title">{{ stock_name }}({{ stock_code }}) - 财务指标</h2>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <a href="{% url 'financial_analysis:financial_report_list' %}" class="btn btn-primary">
          <i class="ti ti-arrow-left"></i>
          返回列表
        </a>
      </div>
    </div>
  </div>

  <!-- 数据采集状态提示 -->
  {% if data_status_message %}
  <div class="alert alert-{{ data_status|default:'info' }} alert-dismissible" role="alert">
    {{ data_status_message }}
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
  </div>
  {% endif %}

  <div class="page-body">
    <!-- 图表展示 -->
    <div class="card mb-3">
      <div class="card-header">
        <h3 class="card-title">财务指标趋势</h3>
      </div>
      <div class="card-body">
        <div id="financial-chart" style="height: 400px"></div>
      </div>
    </div>

    <!-- 历史数据表格 -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">历史数据</h3>
      </div>
      <div class="card-body p-0">
        <div class="table-responsive">
          <table class="table table-vcenter card-table">
            <thead>
              <tr>
                <th>报告期</th>
                <th class="text-end">营业收入(亿)</th>
                <th class="text-end">收入增长率(%)</th>
                <th class="text-end">总资产(亿)</th>
                <th class="text-end">经营现金流(亿)</th>
                <th class="text-end">每股收益</th>
                <th class="text-end">每股净资产</th>
                <th class="text-end">销售净利率(%)</th>
                <th class="text-end">销售毛利率(%)</th>
                <th class="text-end">ROE(%)</th>
              </tr>
            </thead>
            <tbody>
              {% for indicator in indicators %}
              <tr
                class="cursor-pointer"
                data-bs-toggle="modal"
                data-bs-target="#detailModal"
                data-report-date="{{ indicator.report_date }}"
                data-total-revenue="{{ indicator.total_revenue }}"
                data-total-revenue-growth="{{ indicator.total_revenue_growth }}"
                data-total-assets="{{ indicator.total_assets }}"
                data-operating-cash-flow="{{ indicator.operating_cash_flow }}"
                data-eps="{{ indicator.eps }}"
                data-nav="{{ indicator.nav }}"
                data-capital-reserve="{{ indicator.capital_reserve }}"
                data-undistributed-profit="{{ indicator.undistributed_profit }}"
                data-ocf-per-share="{{ indicator.ocf_per_share }}"
                data-net-profit-margin="{{ indicator.net_profit_margin }}"
                data-gross-profit-margin="{{ indicator.gross_profit_margin }}"
                data-roe="{{ indicator.roe }}"
                data-diluted-roe="{{ indicator.diluted_roe }}"
                data-operating-cycle="{{ indicator.inventory_days|add:indicator.receivable_days }}"
                data-inventory-turnover="{{ indicator.inventory_turnover }}"
                data-inventory-days="{{ indicator.inventory_days }}"
                data-receivable-days="{{ indicator.receivable_days }}"
                data-current-ratio="{{ indicator.current_ratio }}"
                data-quick-ratio="{{ indicator.quick_ratio }}"
                data-conservative-quick-ratio="{{ indicator.conservative_quick_ratio }}"
                data-equity-ratio="{{ indicator.equity_ratio }}"
                data-debt-asset-ratio="{{ indicator.debt_asset_ratio }}"
              >
                <td>{{ indicator.report_date }}</td>
                <td class="text-end {% if indicator.total_revenue > 0 %}text-danger{% elif indicator.total_revenue < 0 %}text-success{% endif %}">
                  {{ indicator.total_revenue|floatformat:2|default:"-" }}
                </td>
                <td class="text-end {% if indicator.total_revenue_growth > 0 %}text-danger{% elif indicator.total_revenue_growth < 0 %}text-success{% endif %}">
                  {{ indicator.total_revenue_growth|floatformat:2|default:"-" }}%
                </td>
                <td class="text-end {% if indicator.total_assets > 0 %}text-danger{% elif indicator.total_assets < 0 %}text-success{% endif %}">
                  {{ indicator.total_assets|floatformat:2|default:"-" }}
                </td>
                <td class="text-end {% if indicator.operating_cash_flow > 0 %}text-danger{% elif indicator.operating_cash_flow < 0 %}text-success{% endif %}">
                  {{ indicator.operating_cash_flow|floatformat:2|default:"-" }}
                </td>
                <td class="text-end {% if indicator.eps > 0 %}text-danger{% elif indicator.eps < 0 %}text-success{% endif %}">
                  {{ indicator.eps|floatformat:3|default:"-" }}
                </td>
                <td class="text-end {% if indicator.nav > 0 %}text-danger{% elif indicator.nav < 0 %}text-success{% endif %}">
                  {{ indicator.nav|floatformat:2|default:"-" }}
                </td>
                <td class="text-end {% if indicator.net_profit_margin > 0 %}text-danger{% elif indicator.net_profit_margin < 0 %}text-success{% endif %}">
                  {{ indicator.net_profit_margin|floatformat:2|default:"-" }}%
                </td>
                <td class="text-end {% if indicator.gross_profit_margin > 0 %}text-danger{% elif indicator.gross_profit_margin < 0 %}text-success{% endif %}">
                  {{ indicator.gross_profit_margin|floatformat:2|default:"-" }}%
                </td>
                <td class="text-end {% if indicator.roe > 0 %}text-danger{% elif indicator.roe < 0 %}text-success{% endif %}">
                  {{ indicator.roe|floatformat:2|default:"-" }}%
                </td>
              </tr>
              {% empty %}
              <tr>
                <td colspan="10" class="text-center">暂无数据</td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 详细信息模态框 -->
<div class="modal modal-blur fade" id="detailModal" tabindex="-1" role="dialog" aria-hidden="true">
  <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">财务指标详情 - <span id="modalReportDate"></span></h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="datagrid">
          <!-- 盈利能力指标 -->
          <div class="datagrid-title">盈利能力指标</div>
          <div class="datagrid-item">
            <div class="datagrid-title">营业收入(亿)</div>
            <div class="datagrid-content" id="modalTotalRevenue"></div>
          </div>
          <div class="datagrid-item">
            <div class="datagrid-title">收入增长率(%)</div>
            <div class="datagrid-content" id="modalTotalRevenueGrowth"></div>
          </div>
          <div class="datagrid-item">
            <div class="datagrid-title">总资产(亿)</div>
            <div class="datagrid-content" id="modalTotalAssets"></div>
          </div>
          <div class="datagrid-item">
            <div class="datagrid-title">经营现金流(亿)</div>
            <div class="datagrid-content" id="modalOperatingCashFlow"></div>
          </div>
          <div class="datagrid-item">
            <div class="datagrid-title">销售净利率(%)</div>
            <div class="datagrid-content" id="modalNetProfitMargin"></div>
          </div>
          <div class="datagrid-item">
            <div class="datagrid-title">销售毛利率(%)</div>
            <div class="datagrid-content" id="modalGrossProfitMargin"></div>
          </div>
          <div class="datagrid-item">
            <div class="datagrid-title">ROE(%)</div>
            <div class="datagrid-content" id="modalRoe"></div>
          </div>
          <div class="datagrid-item">
            <div class="datagrid-title">ROE-摊薄(%)</div>
            <div class="datagrid-content" id="modalDilutedRoe"></div>
          </div>

          <!-- 每股指标 -->
          <div class="datagrid-title">每股指标</div>
          <div class="datagrid-item">
            <div class="datagrid-title">每股收益</div>
            <div class="datagrid-content" id="modalEps"></div>
          </div>
          <div class="datagrid-item">
            <div class="datagrid-title">每股净资产</div>
            <div class="datagrid-content" id="modalNav"></div>
          </div>
          <div class="datagrid-item">
            <div class="datagrid-title">每股资本公积</div>
            <div class="datagrid-content" id="modalCapitalReserve"></div>
          </div>
          <div class="datagrid-item">
            <div class="datagrid-title">每股未分配利润</div>
            <div class="datagrid-content" id="modalUndistributedProfit"></div>
          </div>
          <div class="datagrid-item">
            <div class="datagrid-title">每股经营现金流</div>
            <div class="datagrid-content" id="modalOcfPerShare"></div>
          </div>

          <!-- 营运能力指标 -->
          <div class="datagrid-title">营运能力指标</div>
          <div class="datagrid-item">
            <div class="datagrid-title">营业周期(天)</div>
            <div class="datagrid-content" id="modalOperatingCycle"></div>
          </div>
          <div class="datagrid-item">
            <div class="datagrid-title">存货周转率(次)</div>
            <div class="datagrid-content" id="modalInventoryTurnover"></div>
          </div>
          <div class="datagrid-item">
            <div class="datagrid-title">存货周转天数</div>
            <div class="datagrid-content" id="modalInventoryDays"></div>
          </div>
          <div class="datagrid-item">
            <div class="datagrid-title">应收账款周转天数</div>
            <div class="datagrid-content" id="modalReceivableDays"></div>
          </div>

          <!-- 偿债能力指标 -->
          <div class="datagrid-title">偿债能力指标</div>
          <div class="datagrid-item">
            <div class="datagrid-title">流动比率</div>
            <div class="datagrid-content" id="modalCurrentRatio"></div>
          </div>
          <div class="datagrid-item">
            <div class="datagrid-title">速动比率</div>
            <div class="datagrid-content" id="modalQuickRatio"></div>
          </div>
          <div class="datagrid-item">
            <div class="datagrid-title">保守速动比率</div>
            <div class="datagrid-content" id="modalConservativeQuickRatio"></div>
          </div>
          <div class="datagrid-item">
            <div class="datagrid-title">产权比率</div>
            <div class="datagrid-content" id="modalEquityRatio"></div>
          </div>
          <div class="datagrid-item">
            <div class="datagrid-title">资产负债率(%)</div>
            <div class="datagrid-content" id="modalDebtAssetRatio"></div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">关闭</button>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const chartData = {{ indicators_json|safe }};
    const dates = chartData.map(item => item.report_date);

    const chart = echarts.init(document.getElementById('financial-chart'));
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        }
      },
      legend: {
        data: ['营业收入', '总资产', '经营现金流', '销售净利率', '销售毛利率', 'ROE']
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: dates.reverse(),
        axisLabel: {
          rotate: 45
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '金额(亿元)',
          position: 'left'
        },
        {
          type: 'value',
          name: '比率(%)',
          position: 'right'
        }
      ],
      series: [
        {
          name: '营业收入',
          type: 'bar',
          yAxisIndex: 0,
          data: chartData.map(item => item.total_revenue)
        },
        {
          name: '总资产',
          type: 'bar',
          yAxisIndex: 0,
          data: chartData.map(item => item.total_assets)
        },
        {
          name: '经营现金流',
          type: 'bar',
          yAxisIndex: 0,
          data: chartData.map(item => item.operating_cash_flow)
        },
        {
          name: '销售净利率',
          type: 'line',
          yAxisIndex: 1,
          data: chartData.map(item => item.net_profit_margin)
        },
        {
          name: '销售毛利率',
          type: 'line',
          yAxisIndex: 1,
          data: chartData.map(item => item.gross_profit_margin)
        },
        {
          name: 'ROE',
          type: 'line',
          yAxisIndex: 1,
          data: chartData.map(item => item.roe)
        }
      ]
    };
    chart.setOption(option);

    // 处理模态框数据显示
    document.querySelectorAll('tr[data-bs-toggle="modal"]').forEach(row => {
      row.addEventListener('click', function() {
        const data = this.dataset;
        document.getElementById('modalReportDate').textContent = data.reportDate;
        document.getElementById('modalTotalRevenue').textContent = parseFloat(data.totalRevenue).toFixed(2);
        document.getElementById('modalTotalRevenueGrowth').textContent = parseFloat(data.totalRevenueGrowth).toFixed(2) + '%';
        document.getElementById('modalTotalAssets').textContent = parseFloat(data.totalAssets).toFixed(2);
        document.getElementById('modalOperatingCashFlow').textContent = parseFloat(data.operatingCashFlow).toFixed(2);
        document.getElementById('modalEps').textContent = parseFloat(data.eps).toFixed(3);
        document.getElementById('modalNav').textContent = parseFloat(data.nav).toFixed(2);
        document.getElementById('modalCapitalReserve').textContent = parseFloat(data.capitalReserve).toFixed(2);
        document.getElementById('modalUndistributedProfit').textContent = parseFloat(data.undistributedProfit).toFixed(2);
        document.getElementById('modalOcfPerShare').textContent = parseFloat(data.ocfPerShare).toFixed(2);
        document.getElementById('modalNetProfitMargin').textContent = parseFloat(data.netProfitMargin).toFixed(2) + '%';
        document.getElementById('modalGrossProfitMargin').textContent = parseFloat(data.grossProfitMargin).toFixed(2) + '%';
        document.getElementById('modalRoe').textContent = parseFloat(data.roe).toFixed(2) + '%';
        document.getElementById('modalDilutedRoe').textContent = parseFloat(data.dilutedRoe).toFixed(2) + '%';
        document.getElementById('modalOperatingCycle').textContent = parseFloat(data.operatingCycle).toFixed(2);
        document.getElementById('modalInventoryTurnover').textContent = parseFloat(data.inventoryTurnover).toFixed(2);
        document.getElementById('modalInventoryDays').textContent = parseFloat(data.inventoryDays).toFixed(2);
        document.getElementById('modalReceivableDays').textContent = parseFloat(data.receivableDays).toFixed(2);
        document.getElementById('modalCurrentRatio').textContent = parseFloat(data.currentRatio).toFixed(2);
        document.getElementById('modalQuickRatio').textContent = parseFloat(data.quickRatio).toFixed(2);
        document.getElementById('modalConservativeQuickRatio').textContent = parseFloat(data.conservativeQuickRatio).toFixed(2);
        document.getElementById('modalEquityRatio').textContent = parseFloat(data.equityRatio).toFixed(2);
        document.getElementById('modalDebtAssetRatio').textContent = parseFloat(data.debtAssetRatio).toFixed(2) + '%';

        // 设置数值的颜色（红涨绿跌）
        const setValueColor = (elementId, value) => {
          const element = document.getElementById(elementId);
          if (parseFloat(value) > 0) {
            element.classList.add('text-danger');
            element.classList.remove('text-success');
          } else if (parseFloat(value) < 0) {
            element.classList.add('text-success');
            element.classList.remove('text-danger');
          } else {
            element.classList.remove('text-danger', 'text-success');
          }
        };

        // 为所有数值字段设置颜色
        setValueColor('modalTotalRevenue', data.totalRevenue);
        setValueColor('modalTotalRevenueGrowth', data.totalRevenueGrowth);
        setValueColor('modalTotalAssets', data.totalAssets);
        setValueColor('modalOperatingCashFlow', data.operatingCashFlow);
        setValueColor('modalEps', data.eps);
        setValueColor('modalNav', data.nav);
        setValueColor('modalNetProfitMargin', data.netProfitMargin);
        setValueColor('modalGrossProfitMargin', data.grossProfitMargin);
        setValueColor('modalRoe', data.roe);
      });
    });
  });
</script>
{% endblock %}
